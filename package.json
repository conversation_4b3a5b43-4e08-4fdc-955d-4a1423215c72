{"main": "electron.cjs", "name": "futuristic_mac", "description": "A futuristic mac-style desktop app", "author": "<PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "electron": "npm run build && electron .", "pack": "electron-builder --dir", "dist": "electron-builder"}, "dependencies": {"date-fns": "^4.1.0", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-draggable": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "electron": "^37.2.3", "electron-builder": "^26.0.12", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}, "build": {"appId": "com.futuristic.mac", "productName": "FuturisticMac", "directories": {"output": "release"}, "files": ["dist/**/*", "electron.cjs"], "mac": {"target": "dmg"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}