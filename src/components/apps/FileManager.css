.file-manager {
  display: flex;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
}

.file-sidebar {
  width: 200px;
  padding: var(--spacing-md);
  border-right: 1px solid var(--glass-border);
}

.sidebar-section {
  margin-bottom: var(--spacing-lg);
}

.sidebar-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.sidebar-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.file-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.breadcrumb {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--glass-border);
  gap: var(--spacing-xs);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.breadcrumb-link {
  color: var(--text-secondary);
  cursor: pointer;
  transition: color var(--transition-fast);
  font-size: 14px;
}

.breadcrumb-link:hover {
  color: var(--accent-blue);
}

.file-grid {
  flex: 1;
  padding: var(--spacing-md);
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
}

.file-item:hover {
  border-color: var(--accent-blue);
  background: rgba(0, 122, 255, 0.1);
}

.file-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-sm);
  background: rgba(255, 255, 255, 0.05);
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-details {
  display: flex;
  gap: var(--spacing-sm);
  font-size: 12px;
  color: var(--text-tertiary);
}

.file-details span {
  white-space: nowrap;
}

/* Responsive */
@media (max-width: 768px) {
  .file-manager {
    flex-direction: column;
  }
  
  .file-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--glass-border);
  }
  
  .file-grid {
    grid-template-columns: 1fr;
  }
  
  .file-item {
    padding: var(--spacing-sm);
  }
}
