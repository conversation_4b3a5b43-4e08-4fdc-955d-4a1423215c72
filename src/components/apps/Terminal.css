.terminal-app {
  height: 100%;
  background: #000;
  color: #00ff00;
  font-family: 'Monaco', '<PERSON><PERSON>', '<PERSON>solas', monospace;
  font-size: 14px;
  line-height: 1.4;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.terminal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.terminal-title {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
}

.terminal-controls {
  display: flex;
  gap: var(--spacing-xs);
}

.terminal-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.terminal-dot.red {
  background: #ff5f56;
}

.terminal-dot.yellow {
  background: #ffbd2e;
}

.terminal-dot.green {
  background: #27ca3f;
}

.terminal-body {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
  background: linear-gradient(135deg, #000000 0%, #001100 100%);
}

.terminal-line {
  margin-bottom: 2px;
  white-space: pre-wrap;
  word-break: break-all;
}

.terminal-line.input {
  color: #00ff00;
  display: flex;
  align-items: center;
}

.terminal-line.output {
  color: #00ff00;
}

.terminal-line.error {
  color: #ff4444;
}

.terminal-line.typing {
  color: #ffff00;
}

.prompt {
  color: #00aaff;
  margin-right: var(--spacing-sm);
  flex-shrink: 0;
}

.terminal-input {
  background: transparent;
  border: none;
  outline: none;
  color: #00ff00;
  font-family: inherit;
  font-size: inherit;
  flex: 1;
  caret-color: transparent;
}

.terminal-input-form {
  margin-top: auto;
}

.cursor {
  color: #00ff00;
  animation: blink 1s infinite;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.typing-indicator {
  color: #ffff00;
  animation: typing 1.5s infinite;
}

@keyframes typing {
  0%, 33% {
    content: '.';
  }
  34%, 66% {
    content: '..';
  }
  67%, 100% {
    content: '...';
  }
}

/* Matrix effect for special commands */
.terminal-line:has-text("01") {
  color: #00ff00;
  text-shadow: 0 0 10px #00ff00;
  animation: matrix 0.1s infinite;
}

@keyframes matrix {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* Scrollbar styling for terminal */
.terminal-body::-webkit-scrollbar {
  width: 6px;
}

.terminal-body::-webkit-scrollbar-track {
  background: rgba(0, 255, 0, 0.1);
}

.terminal-body::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 0, 0.3);
  border-radius: 3px;
}

.terminal-body::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 0, 0.5);
}

/* Responsive */
@media (max-width: 768px) {
  .terminal-app {
    font-size: 12px;
  }
  
  .terminal-body {
    padding: var(--spacing-sm);
  }
}
