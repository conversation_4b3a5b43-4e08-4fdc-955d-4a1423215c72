.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  overflow: hidden;
}

.loading-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.loading-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.4;
  animation: loadingFloat 8s ease-in-out infinite;
}

.loading-orb.orb-1 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, var(--accent-blue) 0%, transparent 70%);
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.loading-orb.orb-2 {
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, var(--accent-purple) 0%, transparent 70%);
  top: 60%;
  right: 30%;
  animation-delay: -3s;
}

.loading-orb.orb-3 {
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, var(--accent-pink) 0%, transparent 70%);
  bottom: 30%;
  left: 40%;
  animation-delay: -6s;
}

@keyframes loadingFloat {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(30px, -30px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xl);
  z-index: 1;
}

.loading-logo {
  position: relative;
}

.logo-ring {
  width: 120px;
  height: 120px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: logoRotate 4s linear infinite;
  position: relative;
}

.logo-ring::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: conic-gradient(from 0deg, var(--accent-blue), var(--accent-purple), var(--accent-pink), var(--accent-blue));
  animation: logoRotate 4s linear infinite reverse;
  z-index: -1;
}

.logo-inner {
  width: 80px;
  height: 80px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes logoRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-title {
  font-size: 48px;
  font-weight: 300;
  color: var(--text-primary);
  text-align: center;
  letter-spacing: 2px;
  background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.progress-bar {
  width: 300px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
  border-radius: 2px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: progressShimmer 1.5s infinite;
}

.progress-glow {
  position: absolute;
  top: -2px;
  width: 8px;
  height: 8px;
  background: var(--accent-blue);
  border-radius: 50%;
  box-shadow: 0 0 10px var(--accent-blue);
  transform: translateX(-50%);
  transition: left 0.3s ease;
}

@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-text {
  font-size: 14px;
  color: var(--text-secondary);
  font-variant-numeric: tabular-nums;
}

.loading-step {
  font-size: 16px;
  color: var(--text-secondary);
  text-align: center;
  min-height: 24px;
}

.scanning-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.scan-line {
  position: absolute;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);
  opacity: 0.3;
  animation: scanLine 3s linear infinite;
}

.scan-line:nth-child(1) {
  animation-delay: 0s;
}

.scan-line:nth-child(2) {
  animation-delay: 1s;
}

.scan-line:nth-child(3) {
  animation-delay: 2s;
}

@keyframes scanLine {
  0% {
    top: -2px;
    opacity: 0;
  }
  10% {
    opacity: 0.3;
  }
  90% {
    opacity: 0.3;
  }
  100% {
    top: 100%;
    opacity: 0;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .loading-title {
    font-size: 32px;
  }
  
  .progress-bar {
    width: 250px;
  }
  
  .logo-ring {
    width: 80px;
    height: 80px;
  }
  
  .logo-inner {
    width: 60px;
    height: 60px;
  }
}
