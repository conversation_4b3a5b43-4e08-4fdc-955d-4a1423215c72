.system-stats-widget {
  position: relative;
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  min-width: 220px;
  overflow: hidden;
}

.stats-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.stats-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stat-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 12px;
}

.stat-label {
  color: var(--text-secondary);
  flex: 1;
}

.stat-value {
  color: var(--text-primary);
  font-weight: 600;
  font-variant-numeric: tabular-nums;
}

.stat-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.stat-fill {
  height: 100%;
  border-radius: 3px;
  position: relative;
  overflow: hidden;
}

.stat-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.stats-glow {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 60px;
  background: radial-gradient(ellipse, var(--accent-green) 0%, transparent 70%);
  opacity: 0.1;
  animation: statsGlow 5s ease-in-out infinite;
}

@keyframes statsGlow {
  0%, 100% {
    opacity: 0.1;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 0.2;
    transform: translateX(-50%) scale(1.1);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .system-stats-widget {
    min-width: 180px;
    padding: var(--spacing-md);
  }
  
  .stats-title {
    font-size: 14px;
  }
  
  .stat-header {
    font-size: 11px;
  }
}
