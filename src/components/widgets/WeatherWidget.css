.weather-widget {
  position: relative;
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  min-width: 200px;
  overflow: hidden;
}

.weather-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.weather-icon {
  display: flex;
  align-items: center;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.weather-temp {
  font-size: 36px;
  font-weight: 300;
  color: var(--text-primary);
  font-variant-numeric: tabular-nums;
}

.weather-condition {
  font-size: 16px;
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
}

.weather-location {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.weather-details {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.weather-detail {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 12px;
  color: var(--text-secondary);
}

.weather-glow {
  position: absolute;
  top: 20%;
  right: -20px;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, var(--accent-orange) 0%, transparent 70%);
  opacity: 0.1;
  border-radius: 50%;
  animation: weatherPulse 6s ease-in-out infinite;
}

@keyframes weatherPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.2;
  }
}

/* Widget Edit Mode */
.widget-jiggle {
  animation: jiggle 0.5s ease-in-out infinite alternate;
}

@keyframes jiggle {
  0% {
    transform: rotate(-1deg);
  }
  100% {
    transform: rotate(1deg);
  }
}

.widget-lock-button {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--accent-blue);
  color: white;
  border: 2px solid white;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.widget-lock-button:hover {
  background: var(--accent-green);
}

/* Responsive */
@media (max-width: 768px) {
  .weather-widget {
    min-width: 160px;
    padding: var(--spacing-md);
  }

  .weather-temp {
    font-size: 28px;
  }

  .weather-condition {
    font-size: 14px;
  }

  .weather-location {
    font-size: 12px;
  }
}
