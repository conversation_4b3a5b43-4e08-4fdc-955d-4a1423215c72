.clock-widget {
  position: relative;
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  min-width: 200px;
  text-align: center;
  overflow: hidden;
}

.clock-time {
  font-size: 32px;
  font-weight: 300;
  color: var(--text-primary);
  font-variant-numeric: tabular-nums;
  margin-bottom: var(--spacing-sm);
  text-shadow: 0 0 20px rgba(0, 122, 255, 0.3);
}

.clock-date {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
  letter-spacing: 0.5px;
}

.clock-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, var(--accent-blue) 0%, transparent 70%);
  opacity: 0.1;
  border-radius: 50%;
  animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.2;
  }
}

/* Widget Edit Mode */
.widget-jiggle {
  animation: jiggle 0.5s ease-in-out infinite alternate;
}

@keyframes jiggle {
  0% {
    transform: rotate(-1deg);
  }
  100% {
    transform: rotate(1deg);
  }
}

.widget-lock-button {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--accent-blue);
  color: white;
  border: 2px solid white;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.widget-lock-button:hover {
  background: var(--accent-green);
}

/* Responsive */
@media (max-width: 768px) {
  .clock-widget {
    min-width: 160px;
    padding: var(--spacing-md);
  }

  .clock-time {
    font-size: 24px;
  }

  .clock-date {
    font-size: 12px;
  }
}
