.notification-container {
  position: fixed;
  top: 60px;
  right: var(--spacing-md);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-width: 400px;
  pointer-events: none;
}

.notification {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  pointer-events: auto;
  min-width: 300px;
  position: relative;
  overflow: hidden;
}

.notification::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--accent-blue);
}

.notification-success::before {
  background: var(--accent-green);
}

.notification-error::before {
  background: var(--accent-red);
}

.notification-warning::before {
  background: var(--accent-orange);
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  flex: 1;
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-success .notification-icon {
  color: var(--accent-green);
}

.notification-error .notification-icon {
  color: var(--accent-red);
}

.notification-warning .notification-icon {
  color: var(--accent-orange);
}

.notification-info .notification-icon {
  color: var(--accent-blue);
}

.notification-text {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1.4;
}

.notification-message {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
  word-wrap: break-word;
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  flex-shrink: 0;
  margin-top: -2px;
}

.notification-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

/* Progress bar for timed notifications */
.notification::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  animation: notificationProgress 5s linear;
}

@keyframes notificationProgress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Hover effects */
.notification:hover::after {
  animation-play-state: paused;
}

/* Responsive */
@media (max-width: 768px) {
  .notification-container {
    right: var(--spacing-sm);
    left: var(--spacing-sm);
    max-width: none;
  }
  
  .notification {
    min-width: auto;
    padding: var(--spacing-sm);
  }
  
  .notification-title {
    font-size: 13px;
  }
  
  .notification-message {
    font-size: 12px;
  }
}
