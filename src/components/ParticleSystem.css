.particle-system {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
}

.particle-dot {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  filter: blur(0.5px);
}

.cursor-glow {
  position: absolute;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 122, 255, 0.3) 0%, transparent 70%);
  pointer-events: none;
  z-index: 1;
}

/* Additional glow effects */
.particle-dot::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200%;
  height: 200%;
  background: inherit;
  border-radius: 50%;
  opacity: 0.3;
  filter: blur(2px);
}

/* Performance optimizations */
.particle-system * {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}
