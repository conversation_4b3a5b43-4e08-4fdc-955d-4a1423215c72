.menu-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
  z-index: 1000;
  border-radius: 0;
  border: none;
  border-bottom: 1px solid var(--glass-border);
  font-size: 13px;
  font-weight: 500;
}

.menu-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.apple-logo {
  display: flex;
  align-items: center;
  color: var(--text-primary);
  cursor: pointer;
  transition: color var(--transition-fast);
}

.apple-logo:hover {
  color: var(--accent-blue);
}

.app-name {
  color: var(--text-primary);
  font-weight: 600;
  letter-spacing: 0.5px;
}

.menu-center {
  flex: 1;
  display: flex;
  justify-content: center;
  max-width: 400px;
  margin: 0 var(--spacing-xl);
}

.search-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-md);
  width: 100%;
  max-width: 300px;
  transition: all var(--transition-fast);
}

.search-container:focus-within {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--accent-blue);
}

.search-input {
  background: none;
  border: none;
  outline: none;
  color: var(--text-primary);
  font-size: 13px;
  width: 100%;
  placeholder-color: var(--text-tertiary);
}

.search-input::placeholder {
  color: var(--text-tertiary);
}

.menu-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.status-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.status-item.time {
  font-variant-numeric: tabular-nums;
  font-weight: 500;
  min-width: 140px;
  justify-content: flex-end;
}

/* Responsive */
@media (max-width: 768px) {
  .menu-center {
    display: none;
  }
  
  .menu-right {
    gap: var(--spacing-sm);
  }
  
  .status-item span {
    display: none;
  }
  
  .status-item.time {
    min-width: auto;
  }
}
