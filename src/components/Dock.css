.dock {
  position: fixed;
  bottom: var(--spacing-md);
  left: 30%;
  transform: translateX(-50%);
  padding: var(--spacing-md);
  border-radius: var(--radius-xl);
  z-index: 1000;
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
}

.dock-apps {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs);
}

.dock-app {
  position: relative;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all var(--transition-fast);
}

.app-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.app-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  border-radius: inherit;
  pointer-events: none;
}

.app-icon:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.app-tooltip {
  position: absolute;
  bottom: 70px;
  left: 50%;
  transform: translateX(-50%);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  pointer-events: none;
  z-index: 1001;
}

.app-content {
  padding: var(--spacing-xl);
  color: var(--text-primary);
}

.app-content h2 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: 24px;
  font-weight: 600;
}

.app-content p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.terminal-content {
  background: #000;
  color: #00ff00;
  font-family: 'Monaco', 'Menlo', monospace;
  padding: 0;
  border-radius: var(--radius-md);
  overflow: hidden;
}

.terminal-header {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 12px;
  color: var(--text-secondary);
}

.terminal-body {
  padding: var(--spacing-md);
  min-height: 200px;
}

.terminal-body p {
  margin-bottom: var(--spacing-sm);
  color: #00ff00;
}

.terminal-cursor {
  display: inline-block;
  animation: blink 1s infinite;
  color: #00ff00;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Responsive */
@media (max-width: 768px) {
  .dock {
    bottom: var(--spacing-sm);
    padding: var(--spacing-sm);
  }
  
  .dock-apps {
    gap: var(--spacing-xs);
  }
  
  .app-icon {
    width: 48px;
    height: 48px;
  }
  
  .app-icon svg {
    width: 20px;
    height: 20px;
  }
}

/* Settings Grid */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.setting-item {
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.setting-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.setting-item h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 16px;
}

.setting-item p {
  color: var(--text-secondary);
  font-size: 14px;
}

/* Coming Soon Animation */
.coming-soon-animation {
  position: relative;
  width: 100px;
  height: 100px;
  margin: var(--spacing-xl) auto;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid var(--accent-blue);
  border-radius: 50%;
  animation: pulse-ring 2s ease-out infinite;
}

.pulse-ring:nth-child(2) {
  animation-delay: 0.7s;
}

.pulse-ring:nth-child(3) {
  animation-delay: 1.4s;
}

@keyframes pulse-ring {
  0% {
    width: 20px;
    height: 20px;
    opacity: 1;
  }
  100% {
    width: 80px;
    height: 80px;
    opacity: 0;
  }
}
